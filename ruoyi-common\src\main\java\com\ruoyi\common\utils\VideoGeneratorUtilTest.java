package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.vo.GlobalSubtitleVO;
import com.ruoyi.common.vo.MediaResourceVo;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class VideoGeneratorUtilTest {

    public static void main(String[] args) throws Exception {
        // 你的JSON字符串
        String json = "{\"code\":200,\"msg\":\"操作成功\",\"data\":{\"id\":\"1926563302701096960\",\"petName\":\"开心\",\"petType\":2,\"breed\":\"泰迪\",\"gender\":1,\"petId\":\"1926561886527905792\",\"serviceType\":1,\"serviceTime\":\"2025-05-25 16:57:38\",\"beforePhotos\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/4a623d7c850b49ed9f57bdb14d6b7809.jpg\",\"beforeDescription\":\"伊珊娜精致洗护\",\"afterPhotos\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/ff81883e74054393a2a7e9aebcb2b70a.jpg\",\"afterDescription\":\"\",\"notes\":\"\",\"isDeleted\":0,\"createUser\":\"1909185096332656640\",\"createTime\":\"2025-05-25 16:57:40\",\"modifiedUser\":null,\"modifiedTime\":null,\"fkOrgId\":\"1907724611263447040\",\"serviceUserId\":\"1909185096332656640\",\"appointmentId\":\"0\",\"canShare\":0,\"userId\":\"1915551480184619008\",\"contactNumber\":\"13924099528\",\"weight\":2.00,\"isShared\":0,\"shareImage\":null,\"shareTime\":null,\"shareVerifyUser\":null,\"serviceTimeStart\":null,\"serviceTimeEnd\":null,\"keyword\":null,\"orgId\":null,\"serviceUserName\":\"周胜男\",\"genderName\":\"公\",\"petTypeName\":\"狗狗\",\"serviceTypeName\":\"洗护服务\",\"processPhotoVos\":[{\"key\":\"\",\"name\":\"脚底毛修剪\",\"videoUrl\":null,\"beforeUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/29bb4b584ca144a381e62dfd7e94ea1c.jpg\",\"afterUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/e3432e85bf324d82b74902c5e8d6e355.jpg\"},{\"key\":\"\",\"name\":\"指甲修剪\",\"videoUrl\":null,\"beforeUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/d0a9a2c535754f52b8f086307ac99e01.jpg\",\"afterUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/a9fec78b4eab4be8ace2ecb387e2a114.jpg\"},{\"key\":\"\",\"name\":\"驱虫\",\"videoUrl\":\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/a47a0aba77714df2868a93fef9cc79a1.mp4\",\"beforeUrl\":\"\",\"afterUrl\":\"\"}],\"videoUrlList\":null,\"imageUrlList\":[\"https://ylkj-chsh.oss-cn-chengdu.aliyuncs.com/upload/20250525/c7ed224284004871855d2b09738c90bb.jpg\"],\"shareVerifyUserName\":null,\"orgName\":null,\"title\":null,\"storeAddress\":null}}";

        JSONObject obj = JSON.parseObject(json).getJSONObject("data");
        List<MediaResourceVo> resources = new ArrayList<>();
        int order = 1;

        // 进店前
        String beforePhoto = obj.getString("beforePhotos");
        String beforeDesc = obj.getString("beforeDescription");
        if (beforePhoto != null && !beforePhoto.isEmpty()) {
            MediaResourceVo before = new MediaResourceVo();
            before.setType(1);
            before.setUrl(beforePhoto);
            before.setOrder(order++);
            before.setSubtitle("进店前 - " + (beforeDesc != null ? beforeDesc : ""));
            resources.add(before);
        }

        // 护理过程
        JSONArray processArr = obj.getJSONArray("processPhotoVos");
        if (processArr != null) {
            for (int i = 0; i < processArr.size(); i++) {
                JSONObject vo = processArr.getJSONObject(i);
                String name = vo.getString("name");
                String beforeUrl = vo.getString("beforeUrl");
                String afterUrl = vo.getString("afterUrl");
                String videoUrl = vo.getString("videoUrl");

                if (beforeUrl != null && !beforeUrl.isEmpty()) {
                    MediaResourceVo beforeStep = new MediaResourceVo();
                    beforeStep.setType(1);
                    beforeStep.setUrl(beforeUrl);
                    beforeStep.setOrder(order++);
                    beforeStep.setSubtitle(name + " - 前");
                    resources.add(beforeStep);
                }
                if (afterUrl != null && !afterUrl.isEmpty()) {
                    MediaResourceVo afterStep = new MediaResourceVo();
                    afterStep.setType(1);
                    afterStep.setUrl(afterUrl);
                    afterStep.setOrder(order++);
                    afterStep.setSubtitle(name + " - 后");
                    resources.add(afterStep);
                }
                if (videoUrl != null && !videoUrl.isEmpty()) {
                    MediaResourceVo videoStep = new MediaResourceVo();
                    videoStep.setType(2);
                    videoStep.setUrl(videoUrl);
                    videoStep.setOrder(order++);
                    videoStep.setSubtitle(name);
                    resources.add(videoStep);
                }
            }
        }

        // 护理后
        String afterPhoto = obj.getString("afterPhotos");
        String afterDesc = obj.getString("afterDescription");
        if (afterPhoto != null && !afterPhoto.isEmpty()) {
            MediaResourceVo after = new MediaResourceVo();
            after.setType(1);
            after.setUrl(afterPhoto);
            after.setOrder(order++);
            after.setSubtitle("护理后" + (afterDesc != null && !afterDesc.isEmpty() ? " - " + afterDesc : ""));
            resources.add(after);
        }

        // 全局字幕
        GlobalSubtitleVO globalSubtitle = new GlobalSubtitleVO();
        globalSubtitle.setTitle("贵阳宠物洗护新模式");
        globalSubtitle.setContent("洗护、美容、寄养消费都可以积分，积分可兑换，欢迎您的光临！");

        // 输出路径和音频
        String outputPath = "C:\\Users\\<USER>\\desktop\\pet_grooming_" + System.currentTimeMillis() + ".mp4";

        // 尝试多个可能的音频文件路径
        String[] possibleAudioPaths = {
                "C:\\Users\\<USER>\\desktop\\extracted_audio_1748591297575.aac",
        };

        String audioPath = null;
        for (String path : possibleAudioPaths) {
            File audioFile = new File(path);
            if (audioFile.exists()) {
                audioPath = path;
                System.out.println("找到音频文件：" + audioPath);
                System.out.println("音频文件大小：" + (audioFile.length() / 1024) + "KB");
                break;
            }
        }

        // 如果没有找到音频文件，尝试创建一个测试音频文件
        if (audioPath == null) {
            try {
                String testAudioPath = "C:\\Users\\<USER>\\desktop\\test_audio.mp3";
                System.out.println("未找到音频文件，尝试创建测试音频文件：" + testAudioPath);

                // 使用ffmpeg生成一个简单的测试音频文件
                String createAudioCmd = "ffmpeg -y -f lavfi -i sine=frequency=440:duration=10 -c:a aac -b:a 192k \"" + testAudioPath + "\"";
                System.out.println("执行命令：" + createAudioCmd);
                Process process = Runtime.getRuntime().exec(createAudioCmd);

                // 读取命令输出
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        System.out.println("ffmpeg输出: " + line);
                    }
                }

                int exitCode = process.waitFor();
                System.out.println("ffmpeg退出码: " + exitCode);

                if (exitCode == 0 && new File(testAudioPath).exists()) {
                    audioPath = testAudioPath;
                    System.out.println("成功创建测试音频文件：" + audioPath);
                    System.out.println("音频文件大小：" + (new File(testAudioPath).length() / 1024) + "KB");
                } else {
                    System.out.println("创建测试音频文件失败，尝试使用另一种方法");

                    // 尝试另一种方法创建音频
                    String testAudioPath2 = "C:\\Users\\<USER>\\desktop\\test_audio2.mp3";
                    String createAudioCmd2 = "ffmpeg -y -f lavfi -i anullsrc=r=44100:cl=stereo -t 10 -c:a aac -b:a 192k \"" + testAudioPath2 + "\"";
                    System.out.println("执行命令：" + createAudioCmd2);
                    Process process2 = Runtime.getRuntime().exec(createAudioCmd2);

                    // 读取命令输出
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(process2.getErrorStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            System.out.println("ffmpeg输出: " + line);
                        }
                    }
                    int exitCode2 = process2.waitFor();
                    System.out.println("ffmpeg退出码: " + exitCode2);
                    if (exitCode2 == 0 && new File(testAudioPath2).exists()) {
                        audioPath = testAudioPath2;
                        System.out.println("成功创建测试音频文件(方法2)：" + audioPath);
                        System.out.println("音频文件大小：" + (new File(testAudioPath2).length() / 1024) + "KB");
                    } else {
                        System.out.println("创建测试音频文件(方法2)失败，将尝试使用默认路径");
                        audioPath = "C:\\Users\\<USER>\\desktop\\extracted_audio_1748591297575.aac";
                    }
                }
            } catch (Exception e) {
                System.out.println("创建测试音频文件时出错：" + e.getMessage());
                e.printStackTrace();
                audioPath = "C:\\Users\\<USER>\\desktop\\extracted_audio_1748591297575.aac";
            }
        }

        // 验证音频文件是否存在
        File audioFile = new File(audioPath);
        if (!audioFile.exists()) {
            System.out.println("警告：音频文件不存在：" + audioPath);
            System.out.println("将尝试使用默认音频文件");
            // 尝试使用默认音频文件
            audioPath = "C:\\Users\\<USER>\\desktop\\default_audio.aac";
            audioFile = new File(audioPath);
            if (!audioFile.exists()) {
                System.out.println("默认音频文件也不存在，尝试直接创建一个音频文件");
                try {
                    // 尝试直接在当前目录创建一个音频文件
                    String currentDirAudioPath = "test_audio_current_dir.mp3";
                    String createCurrentDirAudioCmd = "ffmpeg -y -f lavfi -i sine=frequency=440:duration=10 -c:a aac -b:a 192k \"" + currentDirAudioPath + "\"";
                    System.out.println("执行命令：" + createCurrentDirAudioCmd);
                    Process currentDirProcess = Runtime.getRuntime().exec(createCurrentDirAudioCmd);
                    int currentDirExitCode = currentDirProcess.waitFor();

                    if (currentDirExitCode == 0 && new File(currentDirAudioPath).exists()) {
                        audioPath = currentDirAudioPath;
                        System.out.println("成功在当前目录创建测试音频文件：" + audioPath);
                        System.out.println("音频文件大小：" + (new File(currentDirAudioPath).length() / 1024) + "KB");
                    } else {
                        System.out.println("在当前目录创建音频文件失败，将生成无音频视频");
                        audioPath = null;
                    }
                } catch (Exception e) {
                    System.out.println("创建当前目录音频文件时出错：" + e.getMessage());
                    audioPath = null;
                }
            } else {
                System.out.println("默认音频文件存在，大小：" + (audioFile.length() / 1024) + "KB");
            }
        } else {
            System.out.println("音频文件存在，大小：" + (audioFile.length() / 1024) + "KB");

            // 验证音频文件是否可用
            try {
                String checkAudioCmd = "ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 \"" + audioPath + "\"";
                System.out.println("执行音频验证命令：" + checkAudioCmd);
                Process checkProcess = Runtime.getRuntime().exec(checkAudioCmd);

                // 读取命令输出
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(checkProcess.getInputStream()))) {
                    String duration = reader.readLine();
                    if (duration != null) {
                        double audioDuration = Double.parseDouble(duration);
                        System.out.println("音频文件有效，时长：" + audioDuration + "秒");
                    }
                }

                // 读取错误输出
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(checkProcess.getErrorStream()))) {
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        errorOutput.append(line).append("\n");
                    }
                }

                int exitCode = checkProcess.waitFor();
                if (exitCode != 0) {
                    System.out.println("音频文件验证失败，错误码：" + exitCode);
                    System.out.println("错误输出：" + errorOutput.toString());
                    System.out.println("将尝试继续使用该音频文件");
                }
            } catch (Exception e) {
                System.out.println("验证音频文件时出错：" + e.getMessage());
                System.out.println("将尝试继续使用该音频文件");
            }
        }

        // 生成视频
        try {
            System.out.println("开始生成视频，使用音频路径：" + audioPath);
            File videoFile = VideoGenerateUtils.generateVideo(resources, outputPath, globalSubtitle, audioPath);
            System.out.println("视频生成成功：" + videoFile.getAbsolutePath());
            System.out.println("视频文件大小：" + (videoFile.length() / 1024 / 1024) + "MB");

            // 验证视频是否包含音频流
            try {
                String checkVideoCmd = "ffprobe -v error -select_streams a -show_entries stream=codec_name -of default=noprint_wrappers=1:nokey=1 \"" + outputPath + "\"";
                System.out.println("执行视频音频流验证命令：" + checkVideoCmd);
                Process checkVideoProcess = Runtime.getRuntime().exec(checkVideoCmd);

                // 读取命令输出
                StringBuilder output = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(checkVideoProcess.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                    }
                }

                // 读取错误输出
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(checkVideoProcess.getErrorStream()))) {
                    String line;
                    while ((line = errorReader.readLine()) != null) {
                        errorOutput.append(line).append("\n");
                    }
                }

                int exitCode = checkVideoProcess.waitFor();
                if (exitCode == 0 && !output.toString().trim().isEmpty()) {
                    System.out.println("视频包含音频流：" + output.toString().trim());
                } else {
                    System.out.println("警告：视频不包含音频流，错误输出：" + errorOutput.toString());

                    // 如果视频不包含音频流，尝试使用多种命令添加音频
                    if (audioPath != null && new File(audioPath).exists()) {
                        System.out.println("尝试使用多种命令添加音频...");

                        // 命令1：最简单的方式
                        String fixAudioCmd1 = String.format(
                                "ffmpeg -y -i \"%s\" -i \"%s\" -c:v copy -c:a aac -strict experimental \"%s_with_audio1.mp4\"",
                                outputPath, audioPath, outputPath.replace(".mp4", "")
                        );
                        System.out.println("执行命令1：" + fixAudioCmd1);
                        Process fixAudioProcess1 = Runtime.getRuntime().exec(fixAudioCmd1);

                        // 读取错误输出
                        StringBuilder fixAudioError1 = new StringBuilder();
                        try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(fixAudioProcess1.getErrorStream()))) {
                            String line;
                            while ((line = errorReader.readLine()) != null) {
                                fixAudioError1.append(line).append("\n");
                                System.out.println("命令1错误输出：" + line);
                            }
                        }

                        int fixAudioExitCode1 = fixAudioProcess1.waitFor();
                        System.out.println("命令1退出码：" + fixAudioExitCode1);

                        if (fixAudioExitCode1 == 0) {
                            System.out.println("命令1音频添加成功，新文件：" + outputPath.replace(".mp4", "") + "_with_audio1.mp4");

                            // 验证新文件是否包含音频流
                            String checkNewCmd1 = "ffprobe -v error -select_streams a -show_entries stream=codec_name -of default=noprint_wrappers=1:nokey=1 \"" + outputPath.replace(".mp4", "") + "_with_audio1.mp4\"";
                            Process checkNewProcess1 = Runtime.getRuntime().exec(checkNewCmd1);
                            StringBuilder newOutput1 = new StringBuilder();
                            try (BufferedReader reader = new BufferedReader(new InputStreamReader(checkNewProcess1.getInputStream()))) {
                                String line;
                                while ((line = reader.readLine()) != null) {
                                    newOutput1.append(line).append("\n");
                                }
                            }
                            int checkNewExit1 = checkNewProcess1.waitFor();
                            if (checkNewExit1 == 0 && !newOutput1.toString().trim().isEmpty()) {
                                System.out.println("新文件1包含音频流：" + newOutput1.toString().trim());
                            } else {
                                System.out.println("警告：新文件1不包含音频流");
                            }
                        } else {
                            System.out.println("命令1音频添加失败，错误码：" + fixAudioExitCode1);
                            System.out.println("错误输出：" + fixAudioError1.toString());

                            // 命令2：使用map参数
                            String fixAudioCmd2 = String.format(
                                    "ffmpeg -y -i \"%s\" -i \"%s\" -c:v copy -map 0:v -map 1:a -c:a aac -ar 44100 -ac 2 -b:a 128k \"%s_with_audio2.mp4\"",
                                    outputPath, audioPath, outputPath.replace(".mp4", "")
                            );
                            System.out.println("执行命令2：" + fixAudioCmd2);
                            Process fixAudioProcess2 = Runtime.getRuntime().exec(fixAudioCmd2);

                            // 读取错误输出
                            StringBuilder fixAudioError2 = new StringBuilder();
                            try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(fixAudioProcess2.getErrorStream()))) {
                                String line;
                                while ((line = errorReader.readLine()) != null) {
                                    fixAudioError2.append(line).append("\n");
                                    System.out.println("命令2错误输出：" + line);
                                }
                            }

                            int fixAudioExitCode2 = fixAudioProcess2.waitFor();
                            System.out.println("命令2退出码：" + fixAudioExitCode2);

                            if (fixAudioExitCode2 == 0) {
                                System.out.println("命令2音频添加成功，新文件：" + outputPath.replace(".mp4", "") + "_with_audio2.mp4");

                                // 验证新文件是否包含音频流
                                String checkNewCmd2 = "ffprobe -v error -select_streams a -show_entries stream=codec_name -of default=noprint_wrappers=1:nokey=1 \"" + outputPath.replace(".mp4", "") + "_with_audio2.mp4\"";
                                Process checkNewProcess2 = Runtime.getRuntime().exec(checkNewCmd2);
                                StringBuilder newOutput2 = new StringBuilder();
                                try (BufferedReader reader = new BufferedReader(new InputStreamReader(checkNewProcess2.getInputStream()))) {
                                    String line;
                                    while ((line = reader.readLine()) != null) {
                                        newOutput2.append(line).append("\n");
                                    }
                                }
                                int checkNewExit2 = checkNewProcess2.waitFor();
                                if (checkNewExit2 == 0 && !newOutput2.toString().trim().isEmpty()) {
                                    System.out.println("新文件2包含音频流：" + newOutput2.toString().trim());
                                } else {
                                    System.out.println("警告：新文件2不包含音频流");
                                }
                            } else {
                                System.out.println("命令2音频添加失败，错误码：" + fixAudioExitCode2);
                                System.out.println("错误输出：" + fixAudioError2.toString());

                                // 命令3：使用filter_complex
                                String fixAudioCmd3 = String.format(
                                        "ffmpeg -y -i \"%s\" -i \"%s\" -filter_complex \"[1:a]aloop=loop=999:size=0[a]\" -map 0:v -map \"[a]\" -c:v copy -c:a aac -ar 44100 -ac 2 -b:a 128k -shortest \"%s_with_audio3.mp4\"",
                                        outputPath, audioPath, outputPath.replace(".mp4", "")
                                );
                                System.out.println("执行命令3：" + fixAudioCmd3);
                                Process fixAudioProcess3 = Runtime.getRuntime().exec(fixAudioCmd3);

                                // 读取错误输出
                                StringBuilder fixAudioError3 = new StringBuilder();
                                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(fixAudioProcess3.getErrorStream()))) {
                                    String line;
                                    while ((line = errorReader.readLine()) != null) {
                                        fixAudioError3.append(line).append("\n");
                                        System.out.println("命令3错误输出：" + line);
                                    }
                                }

                                int fixAudioExitCode3 = fixAudioProcess3.waitFor();
                                System.out.println("命令3退出码：" + fixAudioExitCode3);

                                if (fixAudioExitCode3 == 0) {
                                    System.out.println("命令3音频添加成功，新文件：" + outputPath.replace(".mp4", "") + "_with_audio3.mp4");

                                    // 验证新文件是否包含音频流
                                    String checkNewCmd3 = "ffprobe -v error -select_streams a -show_entries stream=codec_name -of default=noprint_wrappers=1:nokey=1 \"" + outputPath.replace(".mp4", "") + "_with_audio3.mp4\"";
                                    Process checkNewProcess3 = Runtime.getRuntime().exec(checkNewCmd3);
                                    StringBuilder newOutput3 = new StringBuilder();
                                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(checkNewProcess3.getInputStream()))) {
                                        String line;
                                        while ((line = reader.readLine()) != null) {
                                            newOutput3.append(line).append("\n");
                                        }
                                    }
                                    int checkNewExit3 = checkNewProcess3.waitFor();
                                    if (checkNewExit3 == 0 && !newOutput3.toString().trim().isEmpty()) {
                                        System.out.println("新文件3包含音频流：" + newOutput3.toString().trim());
                                    } else {
                                        System.out.println("警告：新文件3不包含音频流");
                                    }
                                } else {
                                    System.out.println("命令3音频添加失败，错误码：" + fixAudioExitCode3);
                                    System.out.println("错误输出：" + fixAudioError3.toString());
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("验证视频音频流时出错：" + e.getMessage());
                e.printStackTrace();
            }
        } catch (Exception e) {
            System.out.println("视频生成过程中发生错误：" + e.getMessage());
            e.printStackTrace();
        }
    }
}



